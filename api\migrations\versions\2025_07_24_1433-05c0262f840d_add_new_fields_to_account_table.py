"""add_new_fields_to_account_table

Revision ID: 05c0262f840d
Revises: 25dc25a92785
Create Date: 2025-07-24 14:33:31.059864

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '05c0262f840d'
down_revision = '25dc25a92785'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('user_id', sa.String(length=36), nullable=True, comment='客户端自定义用户ID'))
        batch_op.create_unique_constraint(batch_op.f('accounts_user_id_key'), ['user_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('accounts_user_id_key'), type_='unique')
        batch_op.drop_column('user_id')

    # ### end Alembic commands ###
