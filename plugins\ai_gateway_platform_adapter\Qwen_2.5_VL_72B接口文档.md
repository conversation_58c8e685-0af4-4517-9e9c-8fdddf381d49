# Qwen_2.5_VL_72B 接口文档

# 接口功能

提供文本/流式推理处理功能。

# 接口格式

接口格式操作类型：POSTURL: https://\{ip\}:\{port\}/v1/chat/completions\{ip\}和\{port\}请使用业务面的IP地址和端口号，即“ipAddress”和“port”。

请求参数  

<table><tr><td colspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td colspan="2">model</td><td>必选</td><td>模型名。</td><td>与 MindIE Server 配置文件中 modelName 的取值保持一致。</td></tr><tr><td colspan="2">messages</td><td>必选</td><td>推理请求消息结构。</td><td>list 类型, 0KB&lt;messages内容包含的字符数&amp;lt;=4MB, 支持中英文。
prompt 经过 tokenizer 之后的 token 数量小于或等于 maxInputTokenLen、maxSeqLen-1、max_position_embedding gs 和 1MB 之间的最小值。其中，max_position_embedding gs 从权重文件 config.json 中获取，其他相关参数从配置文件中获取）。</td></tr><tr><td>-</td><td>role</td><td>必选</td><td>推理请求消息角色。</td><td>字符串类型，可取角色有：</td></tr></table>


</messages>

<div align="right">第1页</div>

<table><tr><td colspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td colspan="2">content</td><td>必选</td><td>推理请求内容。单模态文本模型为string类型，多模态模型为list类型。</td><td>string:
当role为assistant，且tool_calls非空时，content可以不传，其余角色非空。其余情况content非空。
list：请参见使用样例中多模态模型样例。</td></tr><tr><td>-</td><td>type</td><td>可选</td><td>推理请求内容类型。</td><td>text：文本
image_url：图片
video_url：视频
audio_url：音频</td></tr></table>

<table><tr><td></td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;nl&gt;</td></tr><tr><td></td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;nl&gt;</td></tr><tr><td></td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;nl&gt;</td></tr></table>

<div align="right">第2页</div>

<table><tr><td colspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td rowspan="2"></td><td>audio_url</td><td>可选</td><td>推理请求内容为音频。</td><td>持MP4、AVI、WMV，支持URL视频传入，支持HTTP和HTTPS协议。当前支持传入的视频最大512MB。</td></tr><tr><td>tool_calls</td><td>可选</td><td>模型生成的工具调用。</td><td>支持服务器本地路径的音频传入，音频类型支持MP3、WAV、FLAC，支持URL音频传入，支持HTTP和HTTPS协议。当前支持传入的音频最大20MB。</td></tr><tr><td rowspan="3" colspan="2">- function</td><td>必选</td><td>表示模型调用的工具。</td><td>dict类型。
arguments，必选，使用JSON格式的字符串，表示调用函数的参数。
name，必选，字符串，调用的函数名。</td></tr><tr><td>id</td><td>必选</td><td>表示模型某次工具调用的ID。</td><td>字符串。</td></tr><tr><td>type</td><td>必选</td><td>调用的工具类型。</td><td>字符串，仅支持&quot;function&quot;。</td></tr><tr><td colspan="2">tool_call_id</td><td>当 role为 tool时必选，否则</td><td>关联模型某次调用工具时的ID。</td><td>字符串。</td></tr></table>

<div align="right">第3页</div>

<table><tr><td rowspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td>可选</td><td></td><td></td></tr><tr><td>stream</td><td>可选</td><td>指定返回结果是文本推理还是流式推理。</td><td>bool类型参数，可以为 null，默认值 false。
true：流式推理。
false：文本推理。</td></tr><tr><td>presence_penalty</td><td>可选</td><td>存在惩罚介于-2.0和2.0之间，它影响模型如何根据到目前为止是否出现在文本中来惩罚新token。正值将通过惩罚已经使用的词，增加模型谈论新主题的可能性。
不建议同时修改 frequency_penalty 、 repetition_penalty 和 presence_penalty 中的多个参数，可能会影响推理结果。</td><td>float类型，取值范围[-2.0, 2.0]， 默认值0.0，可以为null。</td></tr><tr><td>frequency_penalty</td><td>可选</td><td>频率惩罚介于-2.0和2.0之间，它影响模型如何根据文本中词汇的现有频率惩罚新词汇。正值将通过惩罚已经频繁使用的词来降低模型一行中重复用词的可能性。</td><td>float类型，取值范围[-2.0, 2.0]， 默认值0.0，可以为null。</td></tr></table>

<div align="right">第4页</div>

<table><tr><td>参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td></td><td></td><td>不建议同时修改 frequency_penalty 、 repetition_penalty 和 presence_penalty 中的多个参数，可能会影响推理结果。</td><td></td></tr><tr><td>repetition_penalty</td><td>可选</td><td>重复惩罚用于减少在文本生成过程中出现重复片段的概率。它对之前已经生成的文本进行惩罚，使得模型更倾向于选择新的、不重复的内容。不建议同时修改 frequency_penalty 、 repetition_penalty 和 presence_penalty 中的多个参数，可能会影响推理结果。</td><td>float类型，取值范围 (0.0, 2.0)，默认值 1.0，可以为 null。</td></tr><tr><td>temperature</td><td>可选</td><td>控制生成的随机性，较高的值会产生更多样化的输出。不建议同时修改该值与 top_p。</td><td>float类型，取值范围大于或等于 0.0，默认值 1.0，可以为 null。取值越大，结果的随机性越大。推荐使用大于或等于 0.001 的值，小于 0.001 可能会导致文本质量不佳。</td></tr><tr><td>top_p</td><td>可选</td><td>控制模型生成过程中考虑的词汇</td><td>float类型，取值范围 (1e-6, 1.0)，默认值 1.0，</td></tr></table>

<div align="right">第5页</div>

<table><tr><td rowspan="2">参数</td><td rowspan="2">是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td>范围，使用累计概率选择候选词，直到累计概率超过给定的阈值。该参数也可以控制生成结果的多样性，它基于累积概率选择候选词，直到累计概率超过给定的阈值为止。不建议同时修改该值与 temperature。</td><td>可以为 null。</td></tr><tr><td>top_k</td><td>可选</td><td>控制模型生成过程中考虑的词汇范围，只从概率最高的k个候选词中选择。</td><td>int32 类型，取值范围-1或(0,2147483647)，可以为null。字段未设置时，默认值由后端模型确定，详情请参见说明。取值大于或等于vocabSize时，默认值为vocabSize。
若传-1，-1会变为0传递给MindIE LLM后端，MindIE LLM后端会当做词表大小vocabSize来处理。
vocabSize是从modelWeightPath路径下的config.json文件中读取的vocab_size或者padded_vocab_size的值。建议用户在config.json文件中添加vocab_size或者padded_vocab_size参数，否则可能导致推理失败。</td></tr></table>

<div align="right">第6页</div>

<table><tr><td>参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td>seed</td><td>可选</td><td>用于指定推理过程的随机种子，相同的 seed 值可以确保推理结果的可重现性，不同的 seed 值会提升推理结果的随机性。</td><td>uint64 类型，取值范围 [0, 18446744073709551615]，可以为 null。不传递该参数，系统会产生一个随机 seed 值。当 seed 取到临近最大值时，会有 WARNING，但并不会影响使用。若想去掉 WARNING，可以减小 seed 取值。</td></tr><tr><td>stop</td><td>可选</td><td>停止推理的文本。输出结果默认不包含停止词列表文本。</td><td>List[string]类型或者 string 类型，默认值 null。List[string]：每个元素字符长度大于或等于 1，列表元素总字符长度不超过 32768（32*1024）。列表为空时相当于 null。string：字符长度范围为 1~32768。PD 分离场景暂不支持该参数。</td></tr><tr><td>stop_token_ids</td><td>可选</td><td>停止推理的 token id 列表。输出结果不包含停止推理列表中的 token id。</td><td>List[int32]类型，默认值 null。若该字段值非 null，列表中元素不能为 null，超出 int32 的元素将会被忽略。</td></tr><tr><td>include_stop_str_in_output</td><td>可选</td><td>决定是否在生成的推理文本中包含停止字符串。</td><td>bool 类型，可以为 null，默认值 false。true：包含停止字符串。false：不包含停止字符串。不传入 stop 或 stop_token_ids 时，此字</td></tr></table>

<div align="right">第7页</div>

<table><tr><td colspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td><td></td></tr><tr><td colspan="2">skip_special_tokens</td><td>可选</td><td>指定在推理生成的文本中是否跳过特殊 tokens。</td><td>段会被忽略。
PD 分离场景暂不支持此参数。</td><td></td></tr><tr><td colspan="2">ignore_ EOS</td><td>可选</td><td>指定在推理文本生成过程中是否忽略 eos_token 结束符。</td><td>bool 类型，可以为 null，默认值 true。
true：跳过特殊 tokens。
false：保留特殊 tokens。</td><td></td></tr><tr><td colspan="2">max_tokens</td><td>可选</td><td>允许推理生成的最大 token 个数。实际产生的 token数量同时受到配置文件 maxlterTimes 参数影响，推理 token 个数小于或等于 min(maxlterTimes, max_tokens)。</td><td>bool 类型，可以为 null，默认值 false。
true：忽略 eos_token 结束符。
false：不忽略 eos_token 结束符。</td><td></td></tr><tr><td colspan="2">tools</td><td>可选</td><td>可能会使用的工具列表。</td><td>List[dict]类型，默认值 null。</td><td></td></tr><tr><td rowspan="3">-</td><td>type</td><td>必选</td><td>说明工具类型。</td><td>仅支持字符串 &quot;function&quot;。</td><td></td></tr><tr><td>function</td><td>必选</td><td>函数描述。</td><td>dict 类型。</td><td></td></tr><tr><td>-</td><td>name</td><td>必</td><td>函数名称。</td><td>字符串。</td></tr></table>

<div align="right">第8页</div>

<table><tr><td rowspan="8"></td><td colspan="2">参数</td><td>是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td colspan="2">strict</td><td>可选</td><td>表示生成 tool calls 是否严格遵循 schema 格式。</td><td>bool 类型，默认 false。</td></tr><tr><td colspan="2">description</td><td>可选</td><td>描述函数功能和使用。</td><td>字符串。</td></tr><tr><td colspan="2">parameters</td><td>可选</td><td>表示函数接受的参数。</td><td>JSON schema 格式。</td></tr><tr><td colspan="2">- type</td><td>必选</td><td>表示函数参数属性的类型。</td><td>字符串，仅支持 object。</td></tr><tr><td colspan="2">properties</td><td>必选</td><td>函数参数的属性。每一个key表示一个参数名，由用户自定义。value为dict类型，表示参数描述，包含type和description两个参数。</td><td>dict类型。</td></tr><tr><td colspan="2">required</td><td>必选</td><td>表示函数必填参数列表。</td><td>List[string]类型。</td></tr><tr><td colspan="2">additionalProperties</td><td>可选</td><td>是否允许使用未提及的额外参数。</td><td>bool类型，默认值 false。
true：允许使用未提及的额外参数。
false：不允许使用未提及的额外参数。</td></tr><tr><td colspan="3">tool_choice</td><td>可选</td><td>控制模型调用工具。</td><td>string类型或者dict类型，可以为null，默认值&quot;auto&quot;。
&quot;none&quot;：表示模型不会调用任何工具，而是生成一条消息。</td></tr></table>

<div align="right">第9页</div>

<table><tr><td rowspan="2">参数</td><td rowspan="2">是否必选</td><td>说明</td><td>取值要求</td></tr><tr><td>&quot;auto&quot;: 表示模型可以生成消息或调用一个或多个工具。
&quot;required&quot;: 表示模型必须调用一个或多个工具。
通过{&quot;type&quot;: &quot;function&quot;, &quot;function&quot;: {&quot;name&quot;: &quot;my_function&quot;}}指定特定的工具，将强制模型调用该工具。</td><td></td></tr></table>

# 调用实例

!/bin/bash

app_key="" secret_key="" url="https://{(ip}:{port)/aigateway/predict"

exportLANG=en_US.UTF- 8

curl_date $\equiv$ date- u $^1 +\% a$ %d%b%Y%TGMT#curl_date $\equiv$ date- d'2minutes ago- u $^1 +\% a$ %d%b%Y%TGMT"echo\\$curl_datedate_str $\equiv$ "x- date: $\S \{$ curl_date}"date_base $\equiv$ echo- n{date_n)|openssl dgst- sha256- hmac \\$secret_keybinary|base64curl authorization $\equiv$ hmac username $\equiv$ "\ ${app_key}"algorithm$ \equiv^{\prime} $hmac - sha256"headers$ \equiv^{\prime} $x - date",signature$ \equiv^{\prime\prime}\(\){date_base}"echo \\$curl authorization

curl - - insecure - k - vi - X POST ${curl} - H"x - date:$ {curl_date}" - H"authorization: \\${curl authorization}" - - header 'Content- Type: application/json' - H"Transfer- Encoding: chunked" \ - - data '{ "componentCode":"150023", "model":"QwenVL72B", "messages":[

<div align="right">第10页</div>

{"role":"system","content":"你是一个优秀的生物学家"}，  {"role":"user","content":"我需要一个简单的自我介绍"}  ],  "stream": true  }

示例说明：  app_key 为客户系统编码  secret_key 为客户系统密钥  url 的 ip 地址端口为数智云的调用地址

<div align="right">第11页</div>

