# AI Gateway Platform Adapter

## 概述

AI Gateway Platform Adapter 是一个用于连接南网人工智能平台网关服务的 Dify 插件。该插件通过 HMAC-SHA256 认证方式，提供安全可靠的大模型调用接口。

## 功能特性

### 🔐 安全认证
- **HMAC-SHA256 认证**: 使用客户系统编码和密钥进行安全认证
- **时间戳验证**: 自动生成符合 RFC 标准的 GMT 时间戳
- **签名验证**: 基于 x-date 头部进行 HMAC 签名

### 🤖 模型支持
- **多模态支持**: 支持文本、图像、视频、音频输入
- **流式输出**: 支持实时流式响应
- **工具调用**: 完整的函数调用支持
- **Vision 能力**: 支持图像理解和分析

### ⚙️ 参数控制
- **基础参数**: temperature, top_p, top_k, max_tokens
- **惩罚参数**: presence_penalty, frequency_penalty, repetition_penalty
- **控制参数**: seed, stream, skip_special_tokens, ignore_eos
- **停止控制**: stop, stop_token_ids, include_stop_str_in_output

### 🛠️ 工具调用
- **函数定义**: 支持 JSON Schema 格式的函数定义
- **工具选择**: 支持 auto, none, required 和特定工具选择
- **严格模式**: 支持 strict 模式确保工具调用格式正确

## 配置说明

### 必需参数
- **API Endpoint**: 平台网关服务地址
- **Customer Code**: 客户系统编码
- **Secret Key**: 认证密钥

### 可选参数
- **Context Size**: 上下文窗口大小 (默认: 4096)
- **Temperature**: 温度参数 (0.0-2.0, 默认: 1.0)
- **Top P**: 核采样参数 (0.0-1.0, 默认: 1.0)
- **Top K**: Top-K 采样参数 (-1 或 0-2147483647)
- **Max Tokens**: 最大输出 token 数 (默认: 4096)
- **Presence Penalty**: 存在惩罚 (-2.0-2.0, 默认: 0.0)
- **Frequency Penalty**: 频率惩罚 (-2.0-2.0, 默认: 0.0)
- **Repetition Penalty**: 重复惩罚 (0.0-2.0, 默认: 1.0)
- **Seed**: 随机种子 (0-18446744073709551615)
- **Stream**: 流式输出 (默认: false)
- **Vision Support**: Vision 支持 (默认: no_support)
- **Skip Special Tokens**: 跳过特殊 token (默认: true)
- **Ignore EOS**: 忽略结束符 (默认: false)

## API 接口格式

### 请求格式
```json
{
  "componentCode": "模型组件编码",
  "model": "模型名称",
  "messages": [
    {
      "role": "user",
      "content": "用户输入内容"
    }
  ],
  "stream": true,
  "temperature": 1.0,
  "max_tokens": 4096,
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "函数名称",
        "description": "函数描述",
        "parameters": {
          "type": "object",
          "properties": {},
          "required": []
        }
      }
    }
  ],
  "tool_choice": "auto"
}
```

### 多模态支持
```json
{
  "role": "user",
  "content": [
    {
      "type": "text",
      "text": "描述这张图片"
    },
    {
      "type": "image_url",
      "image_url": "https://example.com/image.jpg"
    }
  ]
}
```

### 认证头部
```
x-date: Mon, 15 Jan 2024 10:30:00 GMT
Authorization: hmac username="customer_code", algorithm="hmac-sha256", headers="x-date", signature="base64_signature"
Content-Type: application/json
```

## 使用示例

### 基础文本对话
```python
# 配置模型
model_config = {
    "api_endpoint": "https://your-platform.com/ai-gateway/predict",
    "customer_code": "your_customer_code",
    "secret_key": "your_secret_key",
    "temperature": 0.7,
    "max_tokens": 1000
}

# 发送请求
messages = [
    {"role": "user", "content": "你好，请介绍一下自己"}
]

response = model.invoke(messages, model_config)
```

### 图像理解
```python
messages = [
    {
        "role": "user",
        "content": [
            {"type": "text", "text": "这张图片里有什么？"},
            {"type": "image_url", "image_url": "https://example.com/image.jpg"}
        ]
    }
]

response = model.invoke(messages, model_config)
```

### 工具调用
```python
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定城市的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        }
    }
]

messages = [
    {"role": "user", "content": "北京今天天气怎么样？"}
]

response = model.invoke(messages, model_config, tools=tools)
```

## 错误处理

插件提供统一的错误处理机制：

- **认证错误**: 401/403 状态码
- **请求错误**: 400 状态码
- **限流错误**: 429 状态码
- **服务错误**: 5xx 状态码
- **连接错误**: 网络连接问题

## 更新日志

### v0.0.2
- ✅ 新增参数支持: presence_penalty, frequency_penalty, repetition_penalty
- ✅ 新增参数支持: top_k, seed, skip_special_tokens, ignore_eos
- ✅ 完善工具调用功能: 支持 tool_choice 和 strict 模式
- ✅ 增强多模态支持: 支持 video_url, audio_url
- ✅ 优化流式响应处理: 支持工具调用的流式输出
- ✅ 改进错误处理: 更精确的错误映射和状态码处理
- ✅ 更新API接口格式: 兼容新的接口规范

### v0.0.1
- ✅ 基础文本对话支持
- ✅ HMAC-SHA256 认证
- ✅ 流式输出支持
- ✅ 多模态输入支持 (文本、图像)
- ✅ 工具调用支持
- ✅ 完整的参数控制
- ✅ 错误处理机制

## 技术支持

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证。 