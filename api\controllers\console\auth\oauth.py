import logging
from datetime import UTC, datetime
from typing import Optional

import requests
from flask import current_app, redirect, request
from flask_restful import Resource
from sqlalchemy import select
from sqlalchemy.orm import Session
from werkzeug.exceptions import Unauthorized

from configs import dify_config
from constants.languages import languages
from events.tenant_event import tenant_was_created
from extensions.ext_database import db
from libs.helper import extract_remote_ip
from libs.oauth import GitHubOAuth, GoogleOAuth, IAMOAuth, OAuthUserInfo
from models import Account
from models.account import AccountStatus
from services.account_service import AccountService, RegisterService, TenantService
from services.errors.account import AccountNotFoundError, AccountRegisterError
from services.errors.workspace import WorkSpaceNotAllowedCreateError, WorkSpaceNotFoundError
from services.feature_service import FeatureService

from .. import api


def get_oauth_providers():
    with current_app.app_context():
        providers = {}

        # GitHub OAuth
        if dify_config.GITHUB_CLIENT_ID and dify_config.GITHUB_CLIENT_SECRET:
            providers["github"] = GitHubOAuth(
                client_id=dify_config.GITHUB_CLIENT_ID,
                client_secret=dify_config.GITHUB_CLIENT_SECRET,
                redirect_uri=dify_config.CONSOLE_API_URL + "/console/api/oauth/authorize/github",
            )

        # Google OAuth
        if dify_config.GOOGLE_CLIENT_ID and dify_config.GOOGLE_CLIENT_SECRET:
            providers["google"] = GoogleOAuth(
                client_id=dify_config.GOOGLE_CLIENT_ID,
                client_secret=dify_config.GOOGLE_CLIENT_SECRET,
                redirect_uri=dify_config.CONSOLE_API_URL + "/console/api/oauth/authorize/google",
            )

        # IAM OAuth
        if dify_config.IAM_CLIENT_ID and dify_config.IAM_CLIENT_SECRET and dify_config.IAM_HOST and dify_config.IAM_NAMESPACE:
            providers["iam"] = IAMOAuth(
                client_id=dify_config.IAM_CLIENT_ID,
                client_secret=dify_config.IAM_CLIENT_SECRET,
                redirect_uri=dify_config.CONSOLE_API_URL + "/console/api/oauth/authorize/iam",
                iam_host=dify_config.IAM_HOST,
                namespace=dify_config.IAM_NAMESPACE
            )

        return providers


class OAuthLogin(Resource):
    def get(self, provider: str):
        invite_token = request.args.get("invite_token") or None
        
        # 支持动态redirect_uri参数
        if provider == "iam":
            # 获取动态redirect_uri参数
            dynamic_redirect_uri = request.args.get("redirect_uri")
            
            # 从配置文件获取其他参数
            if (dify_config.IAM_CLIENT_ID and dify_config.IAM_CLIENT_SECRET and 
                dify_config.IAM_HOST and dify_config.IAM_NAMESPACE):
                
                # 使用动态redirect_uri或默认值
                redirect_uri = dynamic_redirect_uri or (dify_config.CONSOLE_API_URL + "/console/api/oauth/authorize/iam")
                
                oauth_provider = IAMOAuth(
                    client_id=dify_config.IAM_CLIENT_ID,
                    client_secret=dify_config.IAM_CLIENT_SECRET,
                    redirect_uri=redirect_uri,
                    iam_host=dify_config.IAM_HOST,
                    namespace=dify_config.IAM_NAMESPACE
                )
            else:
                # 配置文件参数不完整，回退到原有逻辑
                OAUTH_PROVIDERS = get_oauth_providers()
                oauth_provider = OAUTH_PROVIDERS.get(provider)
        else:
            # 其他提供商使用原有逻辑
            OAUTH_PROVIDERS = get_oauth_providers()
            oauth_provider = OAUTH_PROVIDERS.get(provider)
            
        if not oauth_provider:
            return {"error": "Invalid provider or missing OAuth configuration"}, 400
            
        auth_url = oauth_provider.get_authorization_url(invite_token=invite_token)
        return redirect(auth_url)


class OAuthCallback(Resource):
    def get(self, provider: str):
        code = request.args.get("code")
        state = request.args.get("state")
        invite_token = None
        oauth_provider = None
        
        if provider == "iam" and state:
            try:
                import json
                import base64
                
                # 尝试从state中解码配置信息（新格式）
                state_data = json.loads(base64.b64decode(state.encode()).decode())
                oauth_config = state_data.get("oauth_config")
                invite_token = state_data.get("invite_token")
                
                # 检查是否有动态redirect_uri配置
                if oauth_config and oauth_config.get("redirect_uri"):
                    # 使用动态redirect_uri重建OAuth提供商
                    oauth_provider = IAMOAuth(
                        client_id=dify_config.IAM_CLIENT_ID,
                        client_secret=dify_config.IAM_CLIENT_SECRET,
                        redirect_uri=oauth_config["redirect_uri"],
                        iam_host=dify_config.IAM_HOST,
                        namespace=dify_config.IAM_NAMESPACE
                    )
                else:
                    # 使用默认配置
                    OAUTH_PROVIDERS = get_oauth_providers()
                    oauth_provider = OAUTH_PROVIDERS.get(provider)
                    
            except (json.JSONDecodeError, ValueError, KeyError):
                # state解码失败，可能是旧格式（简单字符串）
                # 回退到配置文件参数，并将state作为invite_token处理
                OAUTH_PROVIDERS = get_oauth_providers()
                oauth_provider = OAUTH_PROVIDERS.get(provider)
                invite_token = state  # 旧格式：state直接是invite_token
        else:
            # 其他提供商或无state参数时使用原有逻辑
            OAUTH_PROVIDERS = get_oauth_providers()
            oauth_provider = OAUTH_PROVIDERS.get(provider)
            if state:
                invite_token = state
        
        if not oauth_provider:
            return {"error": "Invalid provider"}, 400

        try:
            # 处理IAM特殊的token返回格式
            if provider == "iam":
                token_data = oauth_provider.get_access_token(code)
                user_info = oauth_provider.get_user_info(token_data)

                # ===== 修改：使用新的IAM token存储机制 =====
                iam_access_token = token_data.get("access_token")
                iam_refresh_token = token_data.get("refresh_token")
                expires_in = token_data.get("expires_in", 3600)
                
                # 获取完整的IAM用户信息用于存储
                try:
                    iam_user_info = oauth_provider.get_raw_user_info(token_data)
                except:
                    iam_user_info = {}

            else:
                # 标准OAuth流程（GitHub、Google等）
                token = oauth_provider.get_access_token(code)
                user_info = oauth_provider.get_user_info(token)

        except requests.exceptions.RequestException as e:
            error_text = e.response.text if e.response else str(e)
            logging.exception(f"An error occurred during the OAuth process with {provider}: {error_text}")
            return {"error": "OAuth process failed"}, 400

        # 处理邀请令牌（保持原有逻辑不变）
        if invite_token and RegisterService.is_valid_invite_token(invite_token):
            invitation = RegisterService._get_invitation_by_token(token=invite_token)
            if invitation:
                invitation_email = invitation.get("email", None)
                if invitation_email != user_info.email:
                    return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=Invalid invitation token.")

            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin/invite-settings?invite_token={invite_token}")

        try:
            account = _generate_account(provider, user_info)
        except AccountNotFoundError:
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=Account not found.")
        except (WorkSpaceNotAllowedCreateError, WorkSpaceNotFoundError):
            return redirect(
                f"{dify_config.CONSOLE_WEB_URL}/signin"
                "?message=Workspace not found, please contact system admin to invite you to join in a workspace."
            )
        except AccountRegisterError as e:
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message={e.description}")

        # Check account status
        if account.status == AccountStatus.BANNED.value:
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=Account is banned.")

        if account.status == AccountStatus.PENDING.value:
            account.status = AccountStatus.ACTIVE.value
            account.initialized_at = datetime.now(UTC).replace(tzinfo=None)
            db.session.commit()

        try:
            TenantService.create_owner_tenant_if_not_exist(account)
        except Unauthorized:
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=Workspace not found.")
        except WorkSpaceNotAllowedCreateError:
            return redirect(
                f"{dify_config.CONSOLE_WEB_URL}/signin"
                "?message=Workspace not found, please contact system admin to invite you to join in a workspace."
            )

        # ===== 修改：根据provider类型选择登录方式 =====
        if provider == "iam" and iam_access_token and iam_refresh_token:
            # 使用IAM token登录，直接存储IAM token
            token_pair = AccountService.iam_login(
                account=account,
                iam_access_token=iam_access_token,
                iam_refresh_token=iam_refresh_token,
                expires_in=expires_in,
                iam_user_info=iam_user_info,
                ip_address=extract_remote_ip(request),
            )
        else:
            # 使用传统JWT登录
            token_pair = AccountService.login(
                account=account,
                ip_address=extract_remote_ip(request),
            )

        return redirect(
            f"{dify_config.CONSOLE_WEB_URL}?access_token={token_pair.access_token}&refresh_token={token_pair.refresh_token}"
        )


class IAMLogoutResource(Resource):
    """IAM统一登出接口"""

    def get(self):
        try:
            oauth_providers = get_oauth_providers()
            iam_provider = oauth_providers.get("iam")
            if not iam_provider:
                return {"error": "IAM provider not configured"}, 400

            # TODO: 这里需要实现获取当前用户和清除本地会话的逻辑
            # current_user = get_current_user()
            # AccountService.logout(current_user)

            # 生成IAM登出URL并重定向
            redirect_url = f"{dify_config.CONSOLE_WEB_URL}/signin"
            logout_url = iam_provider.get_logout_url(redirect_url)

            return redirect(logout_url)

        except Exception as e:
            logging.exception(f"IAM logout failed: {str(e)}")
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=Logout failed")


class IAMTokenRefreshResource(Resource):
    """IAM Token刷新接口"""

    def post(self):
        try:
            refresh_token = request.json.get('refresh_token')
            if not refresh_token:
                return {"error": "refresh_token is required"}, 400

            oauth_providers = get_oauth_providers()
            iam_provider = oauth_providers.get("iam")
            if not iam_provider:
                return {"error": "IAM provider not configured"}, 400

            # 刷新token
            token_data = iam_provider.refresh_token(refresh_token)

            return {
                "access_token": token_data.get("access_token"),
                "refresh_token": token_data.get("refresh_token"),
                "expires_in": token_data.get("expires_in"),
                "uid": token_data.get("uid")
            }

        except Exception as e:
            logging.exception(f"Token refresh failed: {str(e)}")
            return {"error": "Token refresh failed"}, 500


class IAMCallbackResource(Resource):
    """处理IAM平台的特殊回调接口"""

    def get(self):
        redirect_uri = request.args.get("redirect_uri")
        client_id = request.args.get("client_id")
        access_token = request.args.get("access_token")

        # 验证参数
        if not all([redirect_uri, client_id, access_token]):
            return {"error": "Missing required parameters"}, 400

        # 验证client_id
        if client_id != dify_config.IAM_CLIENT_ID:
            return {"error": "Invalid client_id"}, 400

        try:
            # TODO: 使用access_token处理用户登录逻辑
            # 这里需要根据实际需求实现用户信息获取和账户处理
            logging.info(f"IAM callback received: redirect_uri={redirect_uri}, access_token={access_token[:10]}...")

            # 重定向到指定地址
            return redirect(redirect_uri)

        except Exception as e:
            logging.exception(f"IAM callback failed: {str(e)}")
            return redirect(f"{dify_config.CONSOLE_WEB_URL}/signin?message=OAuth process failed")


def _get_account_by_openid_or_email(provider: str, user_info: OAuthUserInfo) -> Optional[Account]:
    account: Optional[Account] = Account.get_by_openid(provider, user_info.id)

    if not account:
        with Session(db.engine) as session:
            account = session.execute(select(Account).filter_by(email=user_info.email)).scalar_one_or_none()

    return account


def _generate_account(provider: str, user_info: OAuthUserInfo):
    # Get account by openid or email.
    account = _get_account_by_openid_or_email(provider, user_info)

    if account:
        tenants = TenantService.get_join_tenants(account)
        if not tenants:
            if not FeatureService.get_system_features().is_allow_create_workspace:
                raise WorkSpaceNotAllowedCreateError()
            else:
                new_tenant = TenantService.create_tenant(f"{account.name}'s Workspace")
                TenantService.create_tenant_member(new_tenant, account, role="owner")
                account.current_tenant = new_tenant
                tenant_was_created.send(new_tenant)

    if not account:
        if not FeatureService.get_system_features().is_allow_register:
            raise AccountNotFoundError()
        account_name = user_info.name or "Dify"
        account = RegisterService.register(
            email=user_info.email, name=account_name, password=None, open_id=user_info.id, provider=provider
        )

        # Set interface language
        preferred_lang = request.accept_languages.best_match(languages)
        if preferred_lang and preferred_lang in languages:
            interface_language = preferred_lang
        else:
            interface_language = languages[0]
        account.interface_language = interface_language
        db.session.commit()

    # Link account
    AccountService.link_account_integrate(provider, user_info.id, account)

    return account


# 注册标准OAuth路由
api.add_resource(OAuthLogin, "/oauth/login/<provider>")
api.add_resource(OAuthCallback, "/oauth/authorize/<provider>")

# 注册IAM专用路由
api.add_resource(IAMLogoutResource, "/iam/logout")
api.add_resource(IAMTokenRefreshResource, "/iam/refresh-token")
api.add_resource(IAMCallbackResource, "/auth/callback")
