#!/usr/bin/env python3
"""
测试 AI Gateway Platform Adapter 新功能
包括新参数支持和工具调用功能
"""

import json
import httpx
import base64
import hashlib
import hmac
from datetime import datetime, timezone
from typing import Dict, Any

class AiGatewayPlatformTester:
    def __init__(self, api_endpoint: str, customer_code: str, secret_key: str):
        self.api_endpoint = api_endpoint
        self.customer_code = customer_code
        self.secret_key = secret_key
        
    def _generate_hmac_auth(self) -> Dict[str, str]:
        """生成 HMAC-SHA256 认证头部"""
        gmt_format = "%a, %d %b %Y %H:%M:%S GMT"
        x_date = datetime.now(timezone.utc).strftime(gmt_format)
        str_to_sign = f"x-date: {x_date}"

        signature = base64.b64encode(
            hmac.new(self.secret_key.encode(), str_to_sign.encode(), hashlib.sha256).digest()
        ).decode()

        auth_header = f'hmac username="{self.customer_code}", algorithm="hmac-sha256", headers="x-date", signature="{signature}"'

        return {
            "x-date": x_date,
            "Authorization": auth_header,
            "Content-Type": "application/json"
        }
    
    def test_basic_text_completion(self, model: str) -> Dict[str, Any]:
        """测试基础文本对话"""
        print("🧪 测试基础文本对话...")
        
        request_body = {
            "componentCode": model,
            "model": model,
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下自己"}
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        headers = self._generate_hmac_auth()
        
        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    self.api_endpoint,
                    headers=headers,
                    json=request_body
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"❌ 基础文本对话测试失败: {e}")
            return None
    
    def test_new_parameters(self, model: str) -> Dict[str, Any]:
        """测试新参数支持"""
        print("🧪 测试新参数支持...")
        
        request_body = {
            "componentCode": model,
            "model": model,
            "messages": [
                {"role": "user", "content": "请生成一个关于春天的短诗"}
            ],
            "stream": False,
            "temperature": 0.8,
            "top_p": 0.9,
            "top_k": 50,
            "max_tokens": 200,
            "presence_penalty": 0.1,
            "frequency_penalty": 0.1,
            "repetition_penalty": 1.1,
            "seed": 12345,
            "skip_special_tokens": True,
            "ignore_EOS": False
        }
        
        headers = self._generate_hmac_auth()
        
        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    self.api_endpoint,
                    headers=headers,
                    json=request_body
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"❌ 新参数测试失败: {e}")
            return None
    
    def test_tool_calling(self, model: str) -> Dict[str, Any]:
        """测试工具调用功能"""
        print("🧪 测试工具调用功能...")
        
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "获取指定城市的天气信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city": {
                                "type": "string",
                                "description": "城市名称"
                            },
                            "date": {
                                "type": "string",
                                "description": "日期 (YYYY-MM-DD)"
                            }
                        },
                        "required": ["city"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "calculate",
                    "description": "执行数学计算",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string",
                                "description": "数学表达式"
                            }
                        },
                        "required": ["expression"]
                    }
                }
            }
        ]
        
        request_body = {
            "componentCode": model,
            "model": model,
            "messages": [
                {"role": "user", "content": "北京今天天气怎么样？另外请计算 15 * 23 的结果。"}
            ],
            "stream": False,
            "tools": tools,
            "tool_choice": "auto",
            "temperature": 0.1,
            "max_tokens": 300
        }
        
        headers = self._generate_hmac_auth()
        
        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    self.api_endpoint,
                    headers=headers,
                    json=request_body
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"❌ 工具调用测试失败: {e}")
            return None
    
    def test_vision_support(self, model: str) -> Dict[str, Any]:
        """测试 Vision 支持"""
        print("🧪 测试 Vision 支持...")
        
        request_body = {
            "componentCode": model,
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请描述这张图片中的内容"},
                        {"type": "image_url", "image_url": "https://example.com/test-image.jpg"}
                    ]
                }
            ],
            "stream": False,
            "max_tokens": 200,
            "vision_support": "support"
        }
        
        headers = self._generate_hmac_auth()
        
        try:
            with httpx.Client(timeout=30.0, verify=False) as client:
                response = client.post(
                    self.api_endpoint,
                    headers=headers,
                    json=request_body
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"❌ Vision 支持测试失败: {e}")
            return None
    
    def test_streaming_response(self, model: str) -> None:
        """测试流式响应"""
        print("🧪 测试流式响应...")
        
        request_body = {
            "componentCode": model,
            "model": model,
            "messages": [
                {"role": "user", "content": "请写一个关于人工智能的短文，分段落输出"}
            ],
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        headers = self._generate_hmac_auth()
        
        try:
            with httpx.Client(timeout=60.0, verify=False) as client:
                with client.stream(
                    "POST",
                    self.api_endpoint,
                    headers=headers,
                    json=request_body
                ) as response:
                    response.raise_for_status()
                    
                    print("📡 流式响应内容:")
                    for chunk in response.iter_text():
                        if chunk.strip():
                            print(f"   {chunk.strip()}")
                            
        except Exception as e:
            print(f"❌ 流式响应测试失败: {e}")
    
    def run_all_tests(self, model: str) -> None:
        """运行所有测试"""
        print(f"🚀 开始测试 AI Gateway Platform Adapter 新功能")
        print(f"📡 API Endpoint: {self.api_endpoint}")
        print(f"🤖 Model: {model}")
        print("=" * 60)
        
        # 测试基础文本对话
        result = self.test_basic_text_completion(model)
        if result:
            print("✅ 基础文本对话测试通过")
            if "choices" in result and result["choices"]:
                content = result["choices"][0].get("message", {}).get("content", "")
                print(f"📝 响应内容: {content[:100]}...")
        else:
            print("❌ 基础文本对话测试失败")
        
        print("-" * 40)
        
        # 测试新参数
        result = self.test_new_parameters(model)
        if result:
            print("✅ 新参数测试通过")
            if "choices" in result and result["choices"]:
                content = result["choices"][0].get("message", {}).get("content", "")
                print(f"📝 响应内容: {content[:100]}...")
        else:
            print("❌ 新参数测试失败")
        
        print("-" * 40)
        
        # 测试工具调用
        result = self.test_tool_calling(model)
        if result:
            print("✅ 工具调用测试通过")
            if "choices" in result and result["choices"]:
                message = result["choices"][0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                print(f"📝 响应内容: {content[:100]}...")
                if tool_calls:
                    print(f"🛠️ 工具调用数量: {len(tool_calls)}")
                    for i, tool_call in enumerate(tool_calls):
                        func_name = tool_call.get("function", {}).get("name", "")
                        print(f"   {i+1}. {func_name}")
        else:
            print("❌ 工具调用测试失败")
        
        print("-" * 40)
        
        # 测试 Vision 支持
        result = self.test_vision_support(model)
        if result:
            print("✅ Vision 支持测试通过")
        else:
            print("❌ Vision 支持测试失败")
        
        print("-" * 40)
        
        # 测试流式响应
        self.test_streaming_response(model)
        
        print("=" * 60)
        print("🎉 所有测试完成!")

def main():
    """主函数"""
    # 配置信息 - 请根据实际情况修改
    config = {
        "api_endpoint": "https://your-platform.com/ai-gateway/predict",
        "customer_code": "your_customer_code",
        "secret_key": "your_secret_key",
        "model": "QwenVL72B"  # 或其他模型名称
    }
    
    # 创建测试器
    tester = AiGatewayPlatformTester(
        api_endpoint=config["api_endpoint"],
        customer_code=config["customer_code"],
        secret_key=config["secret_key"]
    )
    
    # 运行测试
    tester.run_all_tests(config["model"])

if __name__ == "__main__":
    main() 