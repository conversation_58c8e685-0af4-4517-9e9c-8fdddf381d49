"""add_new_fields_to_account_table

Revision ID: 25dc25a92785
Revises: 58eb7bdb93fe
Create Date: 2025-07-22 14:11:42.696152

"""
from alembic import op
import models as models
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '25dc25a92785'
down_revision = '58eb7bdb93fe'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('login_name', sa.String(length=100), nullable=False, comment='用户登录名，4AId，例如 <EMAIL>'))
        batch_op.add_column(sa.Column('base_org_id', sa.String(length=32), nullable=True, comment='机构ID，组织的唯一ID'))
        batch_op.add_column(sa.Column('start_date', sa.Date(), nullable=True, comment='临时用户生效时间，格式YYYY-MM-DD'))
        batch_op.add_column(sa.Column('end_date', sa.Date(), nullable=True, comment='临时用户失效时间，格式YYYY-MM-DD'))
        batch_op.add_column(sa.Column('gender', sa.String(length=8), nullable=True, comment='性别'))
        batch_op.add_column(sa.Column('identity_no', sa.String(length=30), nullable=True, comment='身份证号'))
        batch_op.add_column(sa.Column('birth_day', sa.Date(), nullable=True, comment='生日，格式YYYY-MM-DD'))
        batch_op.add_column(sa.Column('mobile', sa.String(length=30), nullable=True, comment='手机'))
        batch_op.add_column(sa.Column('office_phone', sa.String(length=30), nullable=True, comment='办公座机'))
        batch_op.add_column(sa.Column('employ_no', sa.String(length=30), nullable=True, comment='工号，用户的员工号'))
        batch_op.add_column(sa.Column('base_post_id', sa.String(length=8), nullable=True, comment='岗位ids'))
        batch_op.add_column(sa.Column('job', sa.String(length=80), nullable=True, comment='职称'))
        batch_op.add_column(sa.Column('duty', sa.String(length=30), nullable=True, comment='职务'))
        batch_op.add_column(sa.Column('degree_code', sa.String(length=30), nullable=True, comment='文化程度'))
        batch_op.add_column(sa.Column('comments', sa.String(length=256), nullable=True, comment='备注'))
        batch_op.add_column(sa.Column('sap_hr_user_id', sa.String(length=32), nullable=True, comment='人资编码，HR唯一ID，对应主数据-员工的MRID'))
        batch_op.add_column(sa.Column('user_type', sa.String(length=256), server_default='EMPLOYEE', nullable=True, comment='用户类型：EMPLOYEE内部用户、TEMP临时用户'))
        batch_op.create_index('account_base_org_id_idx', ['base_org_id'], unique=False)
        batch_op.create_index('account_login_name_idx', ['login_name'], unique=False)
        batch_op.create_unique_constraint(batch_op.f('accounts_login_name_key'), ['login_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f('accounts_login_name_key'), type_='unique')
        batch_op.drop_index('account_login_name_idx')
        batch_op.drop_index('account_base_org_id_idx')
        batch_op.drop_column('user_type')
        batch_op.drop_column('sap_hr_user_id')
        batch_op.drop_column('comments')
        batch_op.drop_column('degree_code')
        batch_op.drop_column('duty')
        batch_op.drop_column('job')
        batch_op.drop_column('base_post_id')
        batch_op.drop_column('employ_no')
        batch_op.drop_column('office_phone')
        batch_op.drop_column('mobile')
        batch_op.drop_column('birth_day')
        batch_op.drop_column('identity_no')
        batch_op.drop_column('gender')
        batch_op.drop_column('end_date')
        batch_op.drop_column('start_date')
        batch_op.drop_column('base_org_id')
        batch_op.drop_column('login_name')

    # ### end Alembic commands ###
