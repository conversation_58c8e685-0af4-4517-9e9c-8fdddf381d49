import secrets
import string
from datetime import datetime
from typing import List, Optional
from flask import request
from flask_restful import Resource, reqparse
from sqlalchemy import func, select
from sqlalchemy.orm import Session

from extensions.ext_database import db
from models.account import Account, AccountStatus
from services.account_service import AccountService, RegisterService
from services.errors.account import AccountNotFoundError, AccountRegisterError
from controllers.console import api
from controllers.console.wraps import setup_required
from libs.passport import PassportService
from werkzeug.exceptions import Unauthorized

# TODO 后续的token验证应当交给iam平台自行解析 适应单点登录流程 参考getToken getUserInfo
def verify_jwt_token():
    """验证JWT token并返回用户信息（已弃用，请使用verify_iam_token）"""
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise Unauthorized("Authorization header is missing")
    
    if not auth_header.startswith("Bearer "):
        raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <token>'")
    
    token = auth_header.split(" ")[1]
    try:
        # 使用PassportService验证token
        passport_service = PassportService()
        payload = passport_service.verify(token)
        
        # 从payload中获取用户ID
        user_id = payload.get("user_id")
        if not user_id:
            raise Unauthorized("Invalid token: missing user_id")
        
        # 加载用户信息
        account = AccountService.load_user(user_id)
        if not account:
            raise Unauthorized("User not found")
        
        return account
    except Exception as e:
        raise Unauthorized(f"Invalid token: {str(e)}")


def verify_iam_token():
    """验证IAM访问令牌并返回用户信息"""
    import requests
    from configs import dify_config
    
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise Unauthorized("Authorization header is missing")
    
    if not auth_header.startswith("Bearer "):
        raise Unauthorized("Invalid Authorization header format. Expected 'Bearer <token>'")
    
    access_token = auth_header.split(" ")[1]
    
    # 检查IAM配置是否完整
    if not all([dify_config.IAM_HOST, dify_config.IAM_NAMESPACE, dify_config.IAM_CLIENT_ID]):
        raise Unauthorized("IAM not configured, please use Dify JWT token")
    
    try:
        # 构建IAM getUserInfo请求URL
        user_info_url = f"http://{dify_config.IAM_HOST}/am-gateway/{dify_config.IAM_NAMESPACE}/am-protocol-service/oauth2/getUserInfo"
        params = {
            "access_token": access_token,
            "client_id": dify_config.IAM_CLIENT_ID
        }
        
        # 调用IAM接口验证令牌并获取用户信息
        response = requests.get(user_info_url, params=params, timeout=10)
        
        if response.status_code != 200:
            raise Unauthorized(f"IAM token validation failed: HTTP {response.status_code}")
        
        iam_user_info = response.json()
        
        # 检查IAM返回的用户信息
        if "userName" not in iam_user_info:
            raise Unauthorized("Invalid IAM response: missing userName")
        
        # 根据IAM用户信息查找Dify用户
        account = find_dify_user_by_iam_info(iam_user_info)
        
        return account
        
    except requests.RequestException as e:
        raise Unauthorized(f"IAM service unavailable: {str(e)}")
    except Exception as e:
        raise Unauthorized(f"IAM token validation failed: {str(e)}")


def find_dify_user_by_iam_info(iam_user_info):
    """根据IAM用户信息查找Dify用户"""
    user_name = iam_user_info.get("userName")
    
    if not user_name:
        raise Unauthorized("Invalid IAM user info: missing userName")
    
    # 首先尝试通过login_name查找用户
    account = db.session.query(Account).filter_by(login_name=user_name).first()
    
    if account:
        # 可选：同步更新用户信息
        sync_iam_user_info(account, iam_user_info)
        return account
    
    # 如果通过login_name找不到，尝试通过email查找
    user_email = iam_user_info.get("email")
    if user_email:
        account = db.session.query(Account).filter_by(email=user_email).first()
        if account:
            # 更新login_name为IAM的userName
            account.login_name = user_name
            db.session.commit()
            sync_iam_user_info(account, iam_user_info)
            return account
    
    # 如果用户不存在，返回错误（要求用户必须先通过用户创建接口创建）
    raise Unauthorized(f"User {user_name} not found in Dify system. Please create user first.")


def sync_iam_user_info(account, iam_user_info):
    """同步IAM用户信息到Dify账户（可选功能）"""
    try:
        # 映射IAM用户信息到Dify字段
        updated = False
        
        # 更新姓名（如果IAM提供了realName）
        real_name = iam_user_info.get("realName")
        if real_name and real_name != account.name:
            account.name = real_name
            updated = True
        
        # 更新邮箱（如果IAM提供了email）
        email = iam_user_info.get("email")
        if email and email != account.email:
            account.email = email
            updated = True
        
        # 可以根据需要添加更多字段同步
        
        if updated:
            db.session.commit()
            
    except Exception as e:
        # 同步失败不影响主流程，只记录日志
        import logging
        logging.warning(f"Failed to sync IAM user info for {account.login_name}: {str(e)}")


def is_iam_token(token):
    """判断是否为IAM访问令牌（简单判断）"""
    # IAM访问令牌通常是32位或更长的随机字符串，不包含JWT的'.'分隔符
    return len(token) >= 32 and '.' not in token


def is_iam_configured():
    """检查IAM是否已配置"""
    from configs import dify_config
    try:
        return all([
            hasattr(dify_config, 'IAM_HOST') and dify_config.IAM_HOST,
            hasattr(dify_config, 'IAM_NAMESPACE') and dify_config.IAM_NAMESPACE,
            hasattr(dify_config, 'IAM_CLIENT_ID') and dify_config.IAM_CLIENT_ID
        ])
    except:
        return False


def verify_token():
    """智能识别并验证令牌类型，支持降级处理"""
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise Unauthorized("Invalid Authorization header")
    
    token = auth_header.split(" ")[1]
    
    # 检查IAM是否已配置
    iam_configured = is_iam_configured()
    
    # 如果IAM已配置且令牌像是IAM令牌，尝试IAM验证
    if iam_configured and is_iam_token(token):
        try:
            return verify_iam_token()
        except Exception as iam_error:
            # IAM验证失败，降级到JWT验证
            import logging
            logging.warning(f"IAM token validation failed, falling back to JWT: {str(iam_error)}")
            try:
                return verify_jwt_token()
            except Exception as jwt_error:
                # 两种验证都失败，返回更友好的错误信息
                raise Unauthorized(f"Token validation failed. IAM error: {str(iam_error)}. JWT error: {str(jwt_error)}")
    
    # 默认使用JWT验证（向后兼容或IAM未配置）
    return verify_jwt_token()


def convert_digital_status_to_db_status(digital_status: str) -> str:
    """将数字平台状态转换为数据库状态"""
    status_mapping = {
        "1": "active",   # 正常
        "2": "banned",   # 禁用
        "3": "locked"    # 锁定
    }
    return status_mapping.get(digital_status, "banned")  # 默认禁用


def convert_db_status_to_digital_status(db_status: str) -> str:
    """将数据库状态转换为数字平台状态"""
    status_mapping = {
        "active": "1",   # 正常
        "banned": "2",   # 禁用
        "locked": "3"    # 锁定
    }
    return status_mapping.get(db_status, "2")  # 默认禁用


class UserQueryService(Resource):
    """用户查询接口 - 业务系统的账号查询接口，主要是数据稽核使用"""
    
    @setup_required
    def post(self):
        # 验证JWT token
        current_user = verify_token()
        
        parser = reqparse.RequestParser()
        parser.add_argument("ids", type=list, location="json", required=False, default=[])
        parser.add_argument("pageSize", type=int, location="json", required=True)
        parser.add_argument("pageNum", type=int, location="json", required=True)
        args = parser.parse_args()

        try:
            # 构建查询
            query = db.session.query(Account)

            # 如果提供了ids数组，添加过滤条件（优先通过user_id查询）
            if args["ids"]:
                user_ids = args["ids"]
                # 优先通过user_id查询
                user_id_query = db.session.query(Account).filter(Account.user_id.in_(user_ids))
                user_id_results = user_id_query.all()
                
                # 如果user_id查询结果不足，再通过系统id查询
                found_ids = {account.user_id for account in user_id_results if account.user_id}
                remaining_ids = [uid for uid in user_ids if uid not in found_ids]
                
                if remaining_ids:
                    system_id_query = db.session.query(Account).filter(Account.id.in_(remaining_ids))
                    system_id_results = system_id_query.all()
                    all_results = user_id_results + system_id_results
                else:
                    all_results = user_id_results
                
                # 去重
                accounts = list({account.id: account for account in all_results}.values())
                
                # 计算总数
                total = len(accounts)
                
                # 分页处理
                offset = (args["pageNum"] - 1) * args["pageSize"]
                accounts = accounts[offset:offset + args["pageSize"]]
            else:
                # 获取总数
                total = query.count()
                
                # 分页查询
                offset = (args["pageNum"] - 1) * args["pageSize"]
                accounts = query.offset(offset).limit(args["pageSize"]).all()

            # 转换为响应格式
            data = []
            for account in accounts:
                data.append({
                    "userId": account.user_id if account.user_id else account.id,  # 优先返回自定义用户ID
                    "loginName": account.login_name or "",
                    "realName": account.name or "",
                    "userStatus": convert_db_status_to_digital_status(account.status),  # 转换为数字状态
                    "gender": account.gender or "",
                    "identityNo": account.identity_no or "",
                    "birthDay": account.birth_day.strftime("%Y-%m-%d") if account.birth_day else "",
                    "mobile": account.mobile or "",
                    "officePhone": account.office_phone or "",
                    "email": account.email or "",
                    "employNo": account.employ_no or "",
                    "basePostId": account.base_post_id or "",
                    "job": account.job or "",
                    "duty": account.duty or "",
                    "degreeCode": account.degree_code or "",
                    "comments": account.comments or "",
                    "baseOrgId": account.base_org_id or "",
                    "sapHRUserId": account.sap_hr_user_id or "",
                    "userType": account.user_type or "",
                    "startDate": account.start_date.strftime("%Y-%m-%d") if account.start_date else "",
                    "endDate": account.end_date.strftime("%Y-%m-%d") if account.end_date else ""
                })

            return {
                "code": 200,
                "status": "success",
                "message": "operation.success",
                "data": {
                    "total": total,
                    "dataNum": len(data),
                    "data": data
                }
            }

        except Exception as e:
            return {
                "code": 500,
                "status": "fail",
                "message": str(e)
            }, 500


class UserCreateService(Resource):
    """用户创建接口 - 业务系统的账号创建接口"""
    
    @setup_required
    def post(self):
        # 验证JWT token
        current_user = verify_token()
        
        parser = reqparse.RequestParser()
        parser.add_argument("userId", type=str, location="json", required=True)  # 用户ID，36位UUID
        parser.add_argument("loginName", type=str, location="json", required=True)  # 用户登录名
        parser.add_argument("realName", type=str, location="json", required=True)  # 姓名
        parser.add_argument("userStatus", type=str, location="json", required=True)  # 用户状态（数字格式）
        parser.add_argument("gender", type=str, location="json", required=False)  # 性别
        parser.add_argument("identityNo", type=str, location="json", required=False)  # 身份证号
        parser.add_argument("birthDay", type=str, location="json", required=False)  # 生日
        parser.add_argument("mobile", type=str, location="json", required=False)  # 手机
        parser.add_argument("officePhone", type=str, location="json", required=False)  # 办公座机
        parser.add_argument("email", type=str, location="json", required=False)  # 用户邮件
        parser.add_argument("employNo", type=str, location="json", required=False)  # 工号
        parser.add_argument("basePostId", type=str, location="json", required=False)  # 岗位ids
        parser.add_argument("job", type=str, location="json", required=False)  # 职称
        parser.add_argument("duty", type=str, location="json", required=False)  # 职务
        parser.add_argument("degreeCode", type=str, location="json", required=False)  # 文化程度
        parser.add_argument("comments", type=str, location="json", required=False)  # 备注
        parser.add_argument("baseOrgId", type=str, location="json", required=False)  # 机构ID
        parser.add_argument("sapHRUserId", type=str, location="json", required=False)  # 人资编码
        parser.add_argument("userType", type=str, location="json", required=False)  # 用户类型
        parser.add_argument("startDate", type=str, location="json", required=False)  # 临时用户生效时间
        parser.add_argument("endDate", type=str, location="json", required=False)  # 临时用户失效时间
        parser.add_argument("serialNum", type=str, location="json", required=True)  # 流水号
        args = parser.parse_args()

        try:
            # 检查用户是否已存在（通过user_id、email、login_name）
            existing_account = db.session.query(Account).filter(
                (Account.user_id == args["userId"]) | 
                (Account.email == args.get("email")) | 
                (Account.login_name == args["loginName"])
            ).first()

            if existing_account:
                return {
                    "code": 500,
                    "message": "用户已存在",
                    "serialNum": args["serialNum"]
                }, 500

            # 如果提供了自定义用户ID，检查是否已存在
            if args.get("userId"):
                existing_user_id = db.session.query(Account).filter_by(id=args["userId"]).first()
                if existing_user_id:
                    return {
                        "code": 500,
                        "message": "指定的用户ID已存在",
                        "serialNum": args["serialNum"]
                    }, 500

            # 生成随机密码
            random_password = ''.join(secrets.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(12))
            
            # 创建账户（使用系统生成的UUID作为主键，设置user_id字段）
            account = Account()
            account.user_id = args["userId"]  # 设置自定义用户ID
            account.email = args.get("email", "")
            account.name = args["realName"]
            account.login_name = args["loginName"]
            # 将数字状态转换为数据库状态
            account.status = convert_digital_status_to_db_status(args["userStatus"])
            account.user_type = args.get("userType", "EMPLOYEE")
            account.interface_language = "zh-Hans"
            account.interface_theme = "light"
            account.timezone = "UTC"
            account.initialized_at = datetime.now()
            
            # 设置可选字段
            account.gender = args.get("gender")
            account.identity_no = args.get("identityNo")
            account.mobile = args.get("mobile")
            account.office_phone = args.get("officePhone")
            account.employ_no = args.get("employNo")
            account.base_post_id = args.get("basePostId")
            account.job = args.get("job")
            account.duty = args.get("duty")
            account.degree_code = args.get("degreeCode")
            account.comments = args.get("comments")
            account.base_org_id = args.get("baseOrgId")
            account.sap_hr_user_id = args.get("sapHRUserId")
            
            # 处理日期字段
            if args.get("birthDay"):
                account.birth_day = datetime.strptime(args["birthDay"], "%Y-%m-%d").date()
            if args.get("startDate"):
                account.start_date = datetime.strptime(args["startDate"], "%Y-%m-%d").date()
            if args.get("endDate"):
                account.end_date = datetime.strptime(args["endDate"], "%Y-%m-%d").date()
            
            # 设置密码
            if random_password:
                import base64
                salt = secrets.token_bytes(16)
                base64_salt = base64.b64encode(salt).decode()
                from libs.password import hash_password
                password_hashed = hash_password(random_password, salt)
                base64_password_hashed = base64.b64encode(password_hashed).decode()
                account.password = base64_password_hashed
                account.password_salt = base64_salt
            
            db.session.add(account)
            db.session.commit()
            
            # 创建租户
            from services.account_service import TenantService
            TenantService.create_owner_tenant_if_not_exist(account=account, is_setup=True)

            return {
                "code": "200",
                "uid": args["userId"],  # 返回入参的userId
                "serialNum": args["serialNum"],
                "message": "success"
            }

        except Exception as e:
            return {
                "code": 500,
                "message": str(e),
                "serialNum": args["serialNum"]
            }, 500


class UserUpdateService(Resource):
    """用户更新接口 - 业务系统的账号更新接口"""
    
    @setup_required
    def post(self):
        # 验证JWT token
        current_user = verify_token()
        
        parser = reqparse.RequestParser()
        parser.add_argument("uid", type=str, location="json", required=True)  # 用户ID
        parser.add_argument("realName", type=str, location="json", required=False)  # 姓名
        parser.add_argument("mobile", type=str, location="json", required=False)  # 手机
        parser.add_argument("userStatus", type=str, location="json", required=False)  # 用户状态（数字格式）
        parser.add_argument("gender", type=str, location="json", required=False)  # 性别
        parser.add_argument("birthDay", type=str, location="json", required=False)  # 生日
        parser.add_argument("officePhone", type=str, location="json", required=False)  # 办公座机
        parser.add_argument("email", type=str, location="json", required=False)  # 用户邮件
        parser.add_argument("serialNum", type=str, location="json", required=True)  # 流水号
        args = parser.parse_args()

        try:
            # 查找用户（优先通过user_id查询，如果找不到再通过系统id查询）
            account = db.session.query(Account).filter(Account.user_id == args["uid"]).first()
            if not account:
                account = db.session.query(Account).filter(Account.id == args["uid"]).first()
            
            if not account:
                return {
                    "code": 500,
                    "message": "用户不存在",
                    "serialNum": args["serialNum"]
                }, 500

            # 构建更新字段
            update_fields = {}
            if args.get("realName"):
                update_fields["name"] = args["realName"]
            if args.get("mobile"):
                update_fields["mobile"] = args["mobile"]
            if args.get("userStatus"):
                # 将数字状态转换为数据库状态
                update_fields["status"] = convert_digital_status_to_db_status(args["userStatus"])
            if args.get("gender"):
                update_fields["gender"] = args["gender"]
            if args.get("birthDay"):
                update_fields["birth_day"] = datetime.strptime(args["birthDay"], "%Y-%m-%d").date()
            if args.get("officePhone"):
                update_fields["office_phone"] = args["officePhone"]
            if args.get("email"):
                update_fields["email"] = args["email"]

            # 更新用户
            if update_fields:
                AccountService.update_account(account, **update_fields)

            return {
                "code": "200",
                "uid": args["uid"],  # 返回入参的uid
                "serialNum": args["serialNum"],
                "message": "success"
            }

        except Exception as e:
            return {
                "code": 500,
                "message": str(e),
                "serialNum": args["serialNum"]
            }, 500


class UserDeleteService(Resource):
    """用户删除接口 - 业务系统的账号删除接口（逻辑删除）"""
    
    @setup_required
    def post(self):
        # 验证JWT token
        current_user = verify_token()
        
        parser = reqparse.RequestParser()
        parser.add_argument("uid", type=str, location="json", required=True)
        parser.add_argument("serialNum", type=str, location="json", required=True)
        args = parser.parse_args()

        try:
            # 查找用户（优先通过user_id查询，如果找不到再通过系统id查询）
            account = db.session.query(Account).filter(Account.user_id == args["uid"]).first()
            if not account:
                account = db.session.query(Account).filter(Account.id == args["uid"]).first()
            
            if not account:
                return {
                    "code": 500,
                    "message": "用户不存在",
                    "serialNum": args["serialNum"]
                }, 500

            # 逻辑删除：修改用户状态为禁用（数据库状态）
            account.status = "banned"  # 设置为禁用状态
            db.session.commit()

            return {
                "code": "200",
                "uid": args["uid"],  # 返回入参的uid
                "serialNum": args["serialNum"],
                "message": "success"
            }

        except Exception as e:
            return {
                "code": 500,
                "message": str(e),
                "serialNum": args["serialNum"]
            }, 500


class UserPhysicalDeleteService(Resource):
    """用户物理删除接口 - 业务系统的账号物理删除接口"""
    
    @setup_required
    def post(self):
        # 验证JWT token
        current_user = verify_token()
        
        parser = reqparse.RequestParser()
        parser.add_argument("uid", type=str, location="json", required=True)
        parser.add_argument("serialNum", type=str, location="json", required=True)
        args = parser.parse_args()

        try:
            # 查找用户（优先通过user_id查询，如果找不到再通过系统id查询）
            account = db.session.query(Account).filter(Account.user_id == args["uid"]).first()
            if not account:
                account = db.session.query(Account).filter(Account.id == args["uid"]).first()
            
            if not account:
                return {
                    "code": 500,
                    "message": "用户不存在",
                    "serialNum": args["serialNum"]
                }, 500

            # 物理删除：直接删除数据库中的用户记录
            # 首先删除相关的租户关联记录
            from models.account import TenantAccountJoin
            db.session.query(TenantAccountJoin).filter_by(account_id=account.id).delete()
            
            # 删除账户集成记录
            from models.account import AccountIntegrate
            db.session.query(AccountIntegrate).filter_by(account_id=account.id).delete()
            
            # 删除账户记录
            db.session.delete(account)
            db.session.commit()

            return {
                "code": "200",
                "uid": args["uid"],  # 返回入参的uid
                "serialNum": args["serialNum"],
                "message": "success"
            }

        except Exception as e:
            db.session.rollback()
            return {
                "code": 500,
                "message": str(e),
                "serialNum": args["serialNum"]
            }, 500


# 注册路由
api.add_resource(UserQueryService, "/UserQueryService")
api.add_resource(UserCreateService, "/UserCreateService")
api.add_resource(UserUpdateService, "/UserUpdateService")
api.add_resource(UserDeleteService, "/UserDeleteService")
api.add_resource(UserPhysicalDeleteService, "/UserPhysicalDeleteService")
